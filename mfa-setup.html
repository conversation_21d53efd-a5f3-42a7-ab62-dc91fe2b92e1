<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>MFA Setup – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <!-- Header -->
    <header class="top-bar">
      <div class="top-bar-content">
        <div class="top-bar-left">
          <button class="back-btn" onclick="goToDashboard()">
            <span class="material-icons">arrow_back</span>
            Dashboard
          </button>
        </div>
        <h1>Multi-Factor Authentication Setup</h1>
        <div id="user-display" class="user-display"></div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="mfa-setup-container">
        
        <!-- MFA Status Card -->
        <div class="card">
          <div class="card-header">
            <h2>
              <span class="material-icons">security</span>
              Security Status
            </h2>
          </div>
          <div class="card-content">
            <div id="mfa-status" class="mfa-status">
              <div class="status-item">
                <span class="material-icons">smartphone</span>
                <div class="status-info">
                  <h3>Authenticator App (TOTP)</h3>
                  <p id="totp-status">Not configured</p>
                </div>
                <button id="totp-action-btn" class="btn-primary" onclick="setupTOTP()">
                  <span class="material-icons">add</span>
                  Setup
                </button>
              </div>

              <div class="status-item">
                <span class="material-icons">fingerprint</span>
                <div class="status-info">
                  <h3>Biometric Authentication</h3>
                  <p id="biometric-status">Not configured</p>
                </div>
                <button id="biometric-action-btn" class="btn-primary" onclick="setupBiometric()">
                  <span class="material-icons">add</span>
                  Setup
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- TOTP Setup Card -->
        <div id="totp-setup-card" class="card" style="display: none;">
          <div class="card-header">
            <h2>
              <span class="material-icons">qr_code</span>
              Setup Authenticator App
            </h2>
          </div>
          <div class="card-content">
            <div class="setup-steps">
              <div class="step">
                <h3>Step 1: Install an Authenticator App</h3>
                <p>Download one of these apps on your phone:</p>
                <ul>
                  <li>Google Authenticator</li>
                  <li>Microsoft Authenticator</li>
                  <li>Authy</li>
                  <li>1Password</li>
                </ul>
              </div>
              
              <div class="step">
                <h3>Step 2: Scan QR Code</h3>
                <div id="qr-code-container" class="qr-code-container">
                  <div id="qr-code"></div>
                </div>
                <p>Or manually enter this secret key:</p>
                <div class="secret-key">
                  <code id="secret-key"></code>
                  <button onclick="copySecret()" class="btn-secondary">
                    <span class="material-icons">content_copy</span>
                    Copy
                  </button>
                </div>
              </div>
              
              <div class="step">
                <h3>Step 3: Verify Setup</h3>
                <p>Enter the 6-digit code from your authenticator app:</p>
                <form id="verify-totp-form">
                  <div class="form-group">
                    <input type="text" id="verify-code" placeholder="123456" maxlength="6" pattern="[0-9]{6}" required>
                  </div>
                  <div class="form-actions">
                    <button type="button" onclick="cancelTOTPSetup()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">
                      <span class="material-icons">verified</span>
                      Verify & Enable
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- Biometric Setup Card -->
        <div id="biometric-setup-card" class="card" style="display: none;">
          <div class="card-header">
            <h2>
              <span class="material-icons">fingerprint</span>
              Setup Biometric Authentication
            </h2>
          </div>
          <div class="card-content">
            <div class="setup-steps">
              <div class="step">
                <h3>Biometric Authentication</h3>
                <p>Use your device's built-in biometric authentication (Face ID, Touch ID, or fingerprint) to securely log in.</p>
                <ul>
                  <li>More secure than passwords</li>
                  <li>Faster login experience</li>
                  <li>Uses your device's secure hardware</li>
                </ul>
              </div>

              <div class="step">
                <h3>Setup Process</h3>
                <p>Click the button below to register your biometric authentication. Your device will prompt you to use your fingerprint, Face ID, or other biometric method.</p>

                <div class="form-actions">
                  <button type="button" onclick="cancelBiometricSetup()" class="btn-secondary">Cancel</button>
                  <button type="button" onclick="registerBiometric()" class="btn-primary">
                    <span class="material-icons">fingerprint</span>
                    Register Biometric
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Credentials Management Card -->
        <div id="credentials-management-card" class="card" style="display: none;">
          <div class="card-header">
            <h2>
              <span class="material-icons">devices</span>
              Manage Biometric Devices
            </h2>
          </div>
          <div class="card-content">
            <p>Here are your registered biometric devices. You can remove devices you no longer use.</p>
            <div id="credentials-list" class="credentials-list"></div>
            <div class="form-actions">
              <button onclick="closeCredentialsManagement()" class="btn-secondary">
                <span class="material-icons">close</span>
                Close
              </button>
              <button onclick="addNewBiometric()" class="btn-primary">
                <span class="material-icons">add</span>
                Add New Device
              </button>
            </div>
          </div>
        </div>

        <!-- Backup Codes Card -->
        <div id="backup-codes-card" class="card" style="display: none;">
          <div class="card-header">
            <h2>
              <span class="material-icons">backup</span>
              Backup Codes
            </h2>
          </div>
          <div class="card-content">
            <p>Save these backup codes in a safe place. You can use them to access your account if you lose your authenticator device.</p>
            <div id="backup-codes-list" class="backup-codes-list"></div>
            <div class="form-actions">
              <button onclick="downloadBackupCodes()" class="btn-secondary">
                <span class="material-icons">download</span>
                Download
              </button>
              <button onclick="printBackupCodes()" class="btn-secondary">
                <span class="material-icons">print</span>
                Print
              </button>
              <button onclick="finishMFASetup()" class="btn-primary">
                <span class="material-icons">check</span>
                I've Saved These Codes
              </button>
            </div>
          </div>
        </div>

      </div>
    </main>

    <!-- Feedback Modal -->
    <div id="feedback-modal" class="modal" style="display: none;">
      <div class="modal-content">
        <div id="feedback-content"></div>
      </div>
    </div>
  </div>

  <!-- SimpleWebAuthn Library -->
  <script src="https://unpkg.com/@simplewebauthn/browser/dist/bundle/index.umd.min.js"></script>

  <!-- QR Code Library -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

  <!-- Include auth utilities -->
  <script src="auth-utils.js"></script>

  <script type="module">
    // Supabase configuration
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    // Import Supabase client
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    // Global variables
    let currentEnrollment = null;
    let backupCodes = [];

    // Initialize page
    document.addEventListener('DOMContentLoaded', async function() {
      // Check authentication
      const isAuthenticated = await window.authUtils.initAuth();
      if (!isAuthenticated) {
        return; // Will redirect to login
      }

      await loadMFAStatus();
      setupEventListeners();
    });

    // Setup event listeners
    function setupEventListeners() {
      document.getElementById('verify-totp-form').addEventListener('submit', verifyTOTP);
    }

    // Load current MFA status
    async function loadMFAStatus() {
      try {
        const { data: factors } = await supabase.auth.mfa.listFactors();

        const totpStatus = document.getElementById('totp-status');
        const totpActionBtn = document.getElementById('totp-action-btn');

        if (factors.totp.length > 0) {
          totpStatus.textContent = 'Configured and active';
          totpStatus.className = 'status-active';
          totpActionBtn.innerHTML = '<span class="material-icons">settings</span>Manage';
          totpActionBtn.onclick = manageTOTP;
        } else {
          totpStatus.textContent = 'Not configured';
          totpStatus.className = 'status-inactive';
        }

        // Check biometric status
        await loadBiometricStatus();
      } catch (error) {
        console.error('Error loading MFA status:', error);
        showFeedback('Error loading MFA status. Please refresh the page.', 'error');
      }
    }

    // Load biometric authentication status
    async function loadBiometricStatus() {
      try {
        const user = window.authState.user;
        const { data: credentials } = await supabase
          .from('webauthn_credentials')
          .select('*')
          .eq('user_id', user.id)
          .eq('is_active', true);

        const biometricStatus = document.getElementById('biometric-status');
        const biometricActionBtn = document.getElementById('biometric-action-btn');

        if (credentials && credentials.length > 0) {
          biometricStatus.textContent = `${credentials.length} device(s) registered`;
          biometricStatus.className = 'status-active';
          biometricActionBtn.innerHTML = '<span class="material-icons">settings</span>Manage';
          biometricActionBtn.onclick = manageBiometric;
        } else {
          biometricStatus.textContent = 'Not configured';
          biometricStatus.className = 'status-inactive';
        }
      } catch (error) {
        console.error('Error loading biometric status:', error);
      }
    }

    // Setup TOTP
    async function setupTOTP() {
      try {
        showFeedback('Setting up authenticator...', 'info');

        const { data, error } = await supabase.auth.mfa.enroll({
          factorType: 'totp',
          friendlyName: 'Authenticator App'
        });

        if (error) throw error;

        currentEnrollment = data;

        // Generate QR code
        const qrCodeUrl = data.totp.qr_code;
        const secret = data.totp.secret;

        document.getElementById('secret-key').textContent = secret;

        // Generate QR code
        const qrCodeElement = document.getElementById('qr-code');
        qrCodeElement.innerHTML = '';
        await QRCode.toCanvas(qrCodeElement, qrCodeUrl, { width: 200 });

        // Show setup card
        document.getElementById('totp-setup-card').style.display = 'block';

        hideFeedback();

      } catch (error) {
        console.error('TOTP setup error:', error);
        showFeedback('Failed to setup authenticator. Please try again.', 'error');
      }
    }

    // Verify TOTP setup
    async function verifyTOTP(event) {
      event.preventDefault();

      const code = document.getElementById('verify-code').value;

      try {
        showFeedback('Verifying code...', 'info');

        const { data, error } = await supabase.auth.mfa.verify({
          factorId: currentEnrollment.id,
          challengeId: currentEnrollment.id,
          code: code
        });

        if (error) throw error;

        // Generate backup codes
        await generateBackupCodes();

        // Update database
        await updateMFAStatus(true);

        // Hide setup card and show backup codes
        document.getElementById('totp-setup-card').style.display = 'none';
        document.getElementById('backup-codes-card').style.display = 'block';

        showFeedback('TOTP setup successful!', 'success');

      } catch (error) {
        console.error('TOTP verification error:', error);
        showFeedback('Invalid code. Please try again.', 'error');
      }
    }

    // Generate backup codes
    async function generateBackupCodes() {
      backupCodes = [];
      for (let i = 0; i < 10; i++) {
        backupCodes.push(generateRandomCode());
      }

      // Display backup codes
      const codesList = document.getElementById('backup-codes-list');
      codesList.innerHTML = backupCodes.map(code =>
        `<div class="backup-code">${code}</div>`
      ).join('');

      // Save to database (hashed)
      await saveBackupCodes();
    }

    // Generate random backup code
    function generateRandomCode() {
      return Math.random().toString(36).substr(2, 8).toUpperCase();
    }

    // Save backup codes to database
    async function saveBackupCodes() {
      try {
        const user = window.authState.user;
        const hashedCodes = await Promise.all(
          backupCodes.map(async code => {
            const encoder = new TextEncoder();
            const data = encoder.encode(code);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            return Array.from(new Uint8Array(hashBuffer))
              .map(b => b.toString(16).padStart(2, '0'))
              .join('');
          })
        );

        for (const hash of hashedCodes) {
          await supabase
            .from('mfa_backup_codes')
            .insert({
              user_id: user.id,
              code_hash: hash
            });
        }
      } catch (error) {
        console.error('Error saving backup codes:', error);
      }
    }

    // Update MFA status in database
    async function updateMFAStatus(enabled) {
      try {
        const user = window.authState.user;
        await supabase
          .from('staff_volunteers')
          .update({
            mfa_enabled: enabled,
            mfa_enrolled_at: enabled ? new Date().toISOString() : null
          })
          .eq('user_id', user.id);
      } catch (error) {
        console.error('Error updating MFA status:', error);
      }
    }

    // Utility functions
    function copySecret() {
      const secret = document.getElementById('secret-key').textContent;
      navigator.clipboard.writeText(secret);
      showFeedback('Secret copied to clipboard!', 'success');
    }

    function cancelTOTPSetup() {
      document.getElementById('totp-setup-card').style.display = 'none';
      currentEnrollment = null;
    }

    function downloadBackupCodes() {
      const content = backupCodes.join('\n');
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'mfa-backup-codes.txt';
      a.click();
      URL.revokeObjectURL(url);
    }

    function printBackupCodes() {
      const content = backupCodes.map(code => `<div>${code}</div>`).join('');
      const printWindow = window.open('', '_blank');
      printWindow.document.write(`
        <html>
          <head><title>MFA Backup Codes</title></head>
          <body>
            <h1>MFA Backup Codes</h1>
            <p>Keep these codes safe. Each can only be used once.</p>
            ${content}
          </body>
        </html>
      `);
      printWindow.print();
    }

    function finishMFASetup() {
      showFeedback('MFA setup complete!', 'success');
      setTimeout(() => {
        window.location.href = 'dashboard.html';
      }, 2000);
    }

    function manageTOTP() {
      // Show management options
      showFeedback('TOTP management coming soon...', 'info');
    }

    // Copy secret key to clipboard
    function copySecret() {
      const secret = document.getElementById('secret-key').textContent;
      navigator.clipboard.writeText(secret).then(() => {
        showFeedback('Secret copied to clipboard!', 'success');
      }).catch(() => {
        showFeedback('Failed to copy secret. Please copy manually.', 'error');
      });
    }

    // Cancel TOTP setup
    function cancelTOTPSetup() {
      document.getElementById('totp-setup-card').style.display = 'none';
      currentEnrollment = null;
    }

    // Download backup codes
    function downloadBackupCodes() {
      const content = backupCodes.join('\n');
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'mfa-backup-codes.txt';
      a.click();
      URL.revokeObjectURL(url);
    }

    // Print backup codes
    function printBackupCodes() {
      const content = backupCodes.map(code => `<div style="margin: 10px 0; font-family: monospace; font-size: 14px;">${code}</div>`).join('');
      const printWindow = window.open('', '_blank');
      printWindow.document.write(`
        <html>
          <head>
            <title>MFA Backup Codes</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { color: #333; }
              .warning { color: #e74c3c; font-weight: bold; margin: 20px 0; }
            </style>
          </head>
          <body>
            <h1>MFA Backup Codes</h1>
            <div class="warning">Keep these codes safe. Each can only be used once.</div>
            ${content}
            <div class="warning">Store these codes in a secure location separate from your device.</div>
          </body>
        </html>
      `);
      printWindow.print();
    }

    // Finish MFA setup
    function finishMFASetup() {
      showFeedback('MFA setup complete!', 'success');
      setTimeout(() => {
        window.location.href = 'dashboard.html';
      }, 2000);
    }

    // Setup biometric authentication
    async function setupBiometric() {
      try {
        // Check if WebAuthn is supported
        if (!window.PublicKeyCredential) {
          throw new Error('WebAuthn is not supported on this device/browser');
        }

        // Check if platform authenticator is available
        const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
        if (!available) {
          throw new Error('No biometric authenticator available on this device');
        }

        // Show setup card
        document.getElementById('biometric-setup-card').style.display = 'block';

      } catch (error) {
        console.error('Biometric setup error:', error);
        showFeedback(error.message || 'Biometric setup not available on this device.', 'error');
      }
    }

    // Register biometric credential
    async function registerBiometric() {
      try {
        showFeedback('Preparing biometric registration...', 'info');

        const user = window.authState.user;
        const profile = window.authState.profile;

        // Create registration options
        const registrationOptions = {
          rp: {
            name: "Cornish Birds of Prey",
            id: window.location.hostname,
          },
          user: {
            id: new TextEncoder().encode(user.id),
            name: user.email,
            displayName: profile.name,
          },
          challenge: new Uint8Array(32),
          pubKeyCredParams: [
            { alg: -7, type: "public-key" }, // ES256
            { alg: -257, type: "public-key" }, // RS256
          ],
          authenticatorSelection: {
            authenticatorAttachment: "platform",
            userVerification: "required",
            requireResidentKey: false,
          },
          timeout: 60000,
          attestation: "direct"
        };

        // Fill challenge with random data
        crypto.getRandomValues(registrationOptions.challenge);

        showFeedback('Please use your biometric authentication to register...', 'info');

        // Start registration
        const registrationResponse = await SimpleWebAuthnBrowser.startRegistration(registrationOptions);

        // Save credential to database
        const credentialId = btoa(String.fromCharCode(...new Uint8Array(registrationResponse.rawId)));
        const publicKey = btoa(String.fromCharCode(...new Uint8Array(registrationResponse.response.publicKey)));

        await supabase
          .from('webauthn_credentials')
          .insert({
            user_id: user.id,
            credential_id: credentialId,
            public_key: publicKey,
            device_type: 'platform',
            device_name: `${navigator.platform} - ${new Date().toLocaleDateString()}`,
            counter: 0
          });

        // Update biometric status in staff_volunteers
        await supabase
          .from('staff_volunteers')
          .update({ biometric_enabled: true })
          .eq('user_id', user.id);

        showFeedback('Biometric authentication registered successfully!', 'success');

        // Hide setup card and refresh status
        document.getElementById('biometric-setup-card').style.display = 'none';
        await loadBiometricStatus();

      } catch (error) {
        console.error('Biometric registration error:', error);
        showFeedback(error.message || 'Failed to register biometric authentication.', 'error');
      }
    }

    function cancelBiometricSetup() {
      document.getElementById('biometric-setup-card').style.display = 'none';
    }

    async function manageBiometric() {
      try {
        await loadCredentialsList();
        document.getElementById('credentials-management-card').style.display = 'block';
      } catch (error) {
        showFeedback('Error loading biometric devices.', 'error');
      }
    }

    // Load and display credentials list
    async function loadCredentialsList() {
      try {
        const user = window.authState.user;
        const { data: credentials } = await supabase
          .from('webauthn_credentials')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        const credentialsList = document.getElementById('credentials-list');

        if (!credentials || credentials.length === 0) {
          credentialsList.innerHTML = '<p>No biometric devices registered.</p>';
          return;
        }

        credentialsList.innerHTML = credentials.map(cred => `
          <div class="credential-item">
            <div class="credential-info">
              <h4>${cred.device_name || 'Unknown Device'}</h4>
              <p>Type: ${cred.device_type}</p>
              <p>Added: ${new Date(cred.created_at).toLocaleDateString()}</p>
              <p>Last used: ${cred.last_used_at ? new Date(cred.last_used_at).toLocaleDateString() : 'Never'}</p>
            </div>
            <div class="credential-actions">
              <button onclick="removeCredential('${cred.id}')" class="btn-danger">
                <span class="material-icons">delete</span>
                Remove
              </button>
            </div>
          </div>
        `).join('');
      } catch (error) {
        console.error('Error loading credentials:', error);
        throw error;
      }
    }

    // Remove a biometric credential
    async function removeCredential(credentialId) {
      try {
        if (!confirm('Are you sure you want to remove this biometric device? You will need to set it up again to use it.')) {
          return;
        }

        showFeedback('Removing device...', 'info');

        await supabase
          .from('webauthn_credentials')
          .update({ is_active: false })
          .eq('id', credentialId);

        // Check if this was the last credential
        const user = window.authState.user;
        const { data: activeCredentials } = await supabase
          .from('webauthn_credentials')
          .select('*')
          .eq('user_id', user.id)
          .eq('is_active', true);

        if (!activeCredentials || activeCredentials.length === 0) {
          // Disable biometric authentication
          await supabase
            .from('staff_volunteers')
            .update({ biometric_enabled: false })
            .eq('user_id', user.id);
        }

        showFeedback('Device removed successfully.', 'success');
        await loadCredentialsList();
        await loadBiometricStatus();

      } catch (error) {
        console.error('Error removing credential:', error);
        showFeedback('Failed to remove device.', 'error');
      }
    }

    function closeCredentialsManagement() {
      document.getElementById('credentials-management-card').style.display = 'none';
    }

    function addNewBiometric() {
      document.getElementById('credentials-management-card').style.display = 'none';
      setupBiometric();
    }

    function goToDashboard() {
      window.location.href = 'dashboard.html';
    }

    function showFeedback(message, type) {
      const modal = document.getElementById('feedback-modal');
      const content = document.getElementById('feedback-content');
      content.innerHTML = `<div class="feedback ${type}">${message}</div>`;
      modal.style.display = 'block';

      if (type === 'success' || type === 'info') {
        setTimeout(hideFeedback, 3000);
      }
    }

    function hideFeedback() {
      document.getElementById('feedback-modal').style.display = 'none';
    }

    // Make functions globally available
    window.setupTOTP = setupTOTP;
    window.copySecret = copySecret;
    window.cancelTOTPSetup = cancelTOTPSetup;
    window.downloadBackupCodes = downloadBackupCodes;
    window.printBackupCodes = printBackupCodes;
    window.finishMFASetup = finishMFASetup;
    window.manageTOTP = manageTOTP;
    window.setupBiometric = setupBiometric;
    window.registerBiometric = registerBiometric;
    window.cancelBiometricSetup = cancelBiometricSetup;
    window.manageBiometric = manageBiometric;
    window.removeCredential = removeCredential;
    window.closeCredentialsManagement = closeCredentialsManagement;
    window.addNewBiometric = addNewBiometric;
    window.goToDashboard = goToDashboard;
  </script>
</body>
</html>
