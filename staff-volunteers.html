<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Staff & Volunteers – Cornish Birds of Prey</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <div class="app-container">
    <!-- Top Navigation -->
    <header class="top-bar">
      <button class="menu-btn" onclick="toggleMenu()">
        <span class="material-icons">menu</span>
      </button>
      <h1>Staff & Volunteers</h1>
      <a href="index.html" class="back-link">
        <span class="material-icons">arrow_back</span>
      </a>
    </header>

    <!-- Main Content -->
    <main class="staff-volunteers-main">
      <!-- Search and Filter Section -->
      <section class="search-filter-section">
        <input class="search-bar" type="text" id="search-input" placeholder="Search staff and volunteers..." />
        
        <div class="filter-section">
          <div class="category-filters">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="Staff">Staff</button>
            <button class="filter-btn" data-filter="Volunteer">Volunteers</button>
            <button class="filter-btn" data-filter="Active">Active</button>
            <button class="filter-btn" data-filter="Inactive">Inactive</button>
          </div>
        </div>
      </section>

      <!-- Staff & Volunteers Grid -->
      <section class="staff-volunteers-grid" id="staff-volunteers-grid">
        <!-- Cards will be dynamically inserted here -->
      </section>

      <!-- Empty State -->
      <div id="empty-state" class="empty-state" style="display: none;">
        <span class="material-icons">people</span>
        <h3>No staff or volunteers found</h3>
        <p>Try adjusting your search or filter criteria</p>
      </div>
    </main>
  </div>

  <!-- Floating Action Buttons -->
  <div class="fab-container">
    <button class="fab" onclick="openAddPersonModal()">
      <span class="material-icons">add</span>Add Person
    </button>
    <button class="fab fab-secondary" onclick="openCreateUserModal()">
      <span class="material-icons">person_add</span>Create User Account
    </button>
  </div>

  <!-- Add Person Modal -->
  <div id="add-person-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeAddPersonModal()">✖</span>
      <h2>Add New Person</h2>
      <form id="add-person-form">
        <div class="form-group">
          <label for="person-name">Full Name *</label>
          <input type="text" id="person-name" name="name" required placeholder="Enter full name">
        </div>

        <div class="form-group">
          <label for="person-role">Role *</label>
          <select id="person-role" name="role" required>
            <option value="">Select Role</option>
            <option value="Staff">Staff</option>
            <option value="Volunteer">Volunteer</option>
          </select>
        </div>

        <div class="form-group">
          <label for="person-position">Position/Responsibility *</label>
          <input type="text" id="person-position" name="position" required placeholder="e.g., Centre Manager, Education Assistant">
        </div>

        <div class="form-group">
          <label for="person-email">Email Address *</label>
          <input type="email" id="person-email" name="email" required placeholder="<EMAIL>">
        </div>

        <div class="form-group">
          <label for="person-phone">Phone Number</label>
          <input type="text" id="person-phone" name="phone_number" placeholder="01234 567890">
        </div>

        <div class="form-group">
          <label for="person-start-date">Start Date *</label>
          <input type="date" id="person-start-date" name="start_date" required>
        </div>

        <div class="form-group">
          <label for="person-photo">Profile Photo</label>
          <div style="margin-bottom: 0.5rem;">
            <button type="button" id="upload-tab" class="photo-tab active-tab" onclick="switchPhotoMethod('upload')">Upload File</button>
            <button type="button" id="url-tab" class="photo-tab" onclick="switchPhotoMethod('url')">Enter URL</button>
          </div>

          <div id="upload-method" style="display: block;">
            <input type="file" name="photo" accept="image/*" id="photo-upload" />
            <small style="color: #666; font-size: 0.9rem;">Choose an image file (JPG, PNG, etc.)</small>
          </div>

          <div id="url-method" style="display: none;">
            <input type="url" name="photo_url" id="photo-url" placeholder="https://example.com/image.jpg" />
            <small style="color: #666; font-size: 0.9rem;">Enter a direct link to an image</small>
          </div>
        </div>

        <div id="photo-preview" style="display: none; margin-bottom: 1rem;">
          <img id="preview-image" style="max-width: 150px; max-height: 150px; border-radius: 50%; border: 2px solid #ddd;" />
          <button type="button" class="remove-photo-btn" onclick="removePhoto()">Remove Photo</button>
        </div>

        <div class="form-group">
          <label for="person-status">Status</label>
          <select id="person-status" name="status">
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
          </select>
        </div>

        <!-- User Attribution will be added here by JavaScript -->

        <div class="form-actions">
          <button type="cancel" class="btn-secondary" onclick="closeAddPersonModal()">Cancel</button>
          <button type="submit" class="btn-primary">Add Person</button>
        </div>
        <div id="person-feedback" class="feedback"></div>
      </form>
    </div>
  </div>

  <!-- Person Detail Modal -->
  <div id="person-detail-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closePersonDetailModal()">✖</span>
      <div id="person-detail-content">
        <!-- Person details will be loaded here -->
      </div>
    </div>
  </div>

  <!-- Create User Account Modal -->
  <div id="create-user-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeCreateUserModal()">✖</span>
      <h2>Create User Account</h2>
      <p class="modal-description">Link a user account to an existing staff or volunteer member.</p>

      <form id="create-user-form">
        <div class="form-group">
          <label for="link-person">Select Staff/Volunteer Member *</label>
          <select id="link-person" name="person_id" required>
            <option value="">Select a person to link...</option>
            <!-- Options will be populated dynamically -->
          </select>
          <small class="form-help">Only staff/volunteer members without user accounts are shown.</small>
        </div>

        <div class="form-group">
          <label for="user-email">Email Address *</label>
          <input type="email" id="user-email" name="email" required placeholder="<EMAIL>">
          <small class="form-help">This will be used for login. Must match the person's email if provided.</small>
        </div>

        <div class="form-group">
          <label for="user-role">User Role *</label>
          <select id="user-role" name="user_role" required>
            <option value="">Select Role</option>
            <option value="Admin">Admin</option>
            <option value="Staff">Staff</option>
            <option value="Volunteer">Volunteer</option>
          </select>
          <small class="form-help">User role should match or be lower than their staff/volunteer role.</small>
        </div>

        <div class="form-group">
          <label for="temp-password">Temporary Password *</label>
          <div class="password-input-container">
            <input type="password" id="temp-password" name="password" required placeholder="Minimum 12 characters">
            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('temp-password')">
              <span class="material-icons">visibility</span>
            </button>
          </div>
          <small class="password-requirements">
            Password must be at least 12 characters with uppercase, lowercase, numbers, and special characters.
          </small>
        </div>

        <div class="form-options">
          <label class="checkbox-container">
            <input type="checkbox" name="requireReset" checked>
            <span class="checkmark"></span>
            Require password reset on first login
          </label>
        </div>

        <!-- User Attribution will be added here by JavaScript -->

        <div class="form-actions">
          <button type="button" class="btn-secondary" onclick="closeCreateUserModal()">Cancel</button>
          <button type="submit" class="btn-primary">
            <span class="material-icons">person_add</span>
            Create User Account
          </button>
        </div>
        <div id="create-user-feedback" class="feedback"></div>
      </form>
    </div>
  </div>

  <!-- GDPR Policy Modal -->
  <div id="gdpr-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal" onclick="closeGDPRModal()">✖</span>
      <h2>GDPR Compliance Statement</h2>
      <div class="gdpr-content">
        <p>The Cornish Birds of Prey Center is committed to protecting your personal data in accordance with GDPR regulations.</p>
        
        <h3>Data Collection</h3>
        <p>We collect and store only essential information required for operational purposes, volunteer coordination, and safeguarding requirements.</p>
        
        <h3>Data Security</h3>
        <p>All personal data is stored securely using encrypted databases and is accessible only to authorized staff members.</p>
        
        <h3>Your Rights</h3>
        <p>You have the right to:</p>
        <ul>
          <li>View your personal data</li>
          <li>Request corrections to your data</li>
          <li>Request deletion of your data</li>
          <li>Withdraw consent for data processing</li>
        </ul>
        
        <h3>Contact</h3>
        <p>To exercise these rights or for any data protection queries, please contact the Centre <NAME_EMAIL> or 01234 567890.</p>
        
        <p><strong>Last Updated:</strong> <span id="gdpr-date"></span></p>
      </div>
      <div class="form-actions">
        <button type="button" class="btn-primary" onclick="closeGDPRModal()">Close</button>
      </div>
    </div>
  </div>

  <!-- Side Menu -->
  <nav id="side-menu" class="side-menu">
    <div class="menu-header">
      <h2>Cornish Birds of Prey</h2>
      <button class="close-menu" onclick="toggleMenu()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <ul class="menu-items">
      <li><a href="dashboard.html"><span class="material-icons">dashboard</span> Dashboard</a></li>
      <li><a href="index.html"><span class="material-icons">pets</span> Animals</a></li>
      <li class="active"><a href="staff-volunteers.html"><span class="material-icons">people</span> Staff & Volunteers</a></li>
      <li><a href="donations-adoptions.html"><span class="material-icons">volunteer_activism</span> Adoptions & Donations</a></li>
      <li><a href="documents.html"><span class="material-icons">description</span> Documents</a></li>
      <li><a href="reports.html"><span class="material-icons">bar_chart</span> Reports</a></li>
      <li><a href="emergency-contacts.html"><span class="material-icons">emergency</span> Emergency Contacts</a></li>
    </ul>
  </nav>

  <!-- Menu Overlay -->
  <div id="menu-overlay" class="menu-overlay" onclick="toggleMenu()"></div>

  <!-- GDPR Footer -->
  <footer class="gdpr-footer">
    <a href="#" onclick="openGDPRModal()">View GDPR Policy</a>
  </footer>

  <script src="auth-utils.js"></script>
  <script type="module">
    import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

    // Supabase configuration
    const SUPABASE_URL = 'https://wkclogfpyykwgjhhshsi.supabase.co';
    const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrY2xvZ2ZweXlrd2dqaGhzaHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMzc4OTQsImV4cCI6MjA2NjcxMzg5NH0.2SEQj0Gm8gZLZELn28JIipJiBkoxHelxWSqIxVYcq-k';

    const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

    // Global variables
    let allStaffVolunteers = [];
    let filteredStaffVolunteers = [];
    let currentFilter = 'all';

    // Initialize page
    async function initializeApp() {
      console.log('Starting staff/volunteers page initialization...');

      // Check authentication
      console.log('Checking authentication...');
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        console.log('User not authenticated, redirecting to login');
        window.location.href = 'login.html';
        return;
      }

      console.log('✅ User authenticated:', user.email);

      await loadStaffVolunteers();
      setupEventListeners();
      setGDPRDate();
      setDefaultStartDate();
    }

    document.addEventListener('DOMContentLoaded', function() {
      initializeApp();
    });

    // Menu toggle function
    function toggleMenu() {
      const menu = document.getElementById('side-menu');
      const overlay = document.getElementById('menu-overlay');
      
      if (menu.classList.contains('open')) {
        menu.classList.remove('open');
        overlay.classList.remove('active');
      } else {
        menu.classList.add('open');
        overlay.classList.add('active');
      }
    }

    // Load all staff and volunteers from Supabase
    async function loadStaffVolunteers() {
      console.log('Fetching staff and volunteers...');

      const { data: staffVolunteers, error } = await supabase
        .from('staff_volunteers')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading staff and volunteers:', error);
        showToast('Error loading staff and volunteers. Please refresh the page.', 'error');
        showEmptyState();
        return;
      }

      console.log('Staff and volunteers loaded:', staffVolunteers.length);
      allStaffVolunteers = staffVolunteers;
      filteredStaffVolunteers = [...allStaffVolunteers];
      renderStaffVolunteers();
    }

    // Render staff and volunteers cards
    function renderStaffVolunteers() {
      const container = document.getElementById('staff-volunteers-grid');
      const emptyState = document.getElementById('empty-state');

      if (filteredStaffVolunteers.length === 0) {
        container.innerHTML = '';
        emptyState.style.display = 'block';
        return;
      }

      emptyState.style.display = 'none';
      container.innerHTML = filteredStaffVolunteers.map(person => createPersonCard(person)).join('');
    }

    // Create individual person card HTML
    function createPersonCard(person) {
      const photoUrl = person.photo_url || '';
      const photoElement = photoUrl
        ? `<img src="${photoUrl}" alt="${person.name}" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover; object-position: top;" />`
        : `<div style="width: 60px; height: 60px; border-radius: 50%; background: rgba(255,255,255,0.3); display: flex; align-items: center; justify-content: center;"><span class="material-icons" style="font-size: 1.5rem; color: #666;">person</span></div>`;

      const roleClass = person.role === 'Staff' ? 'staff-badge' : 'volunteer-badge';
      const statusClass = person.status === 'Active' ? 'status-active' : 'status-inactive';
      const lastLogin = person.last_login ? new Date(person.last_login).toLocaleDateString('en-GB') : 'Never';

      return `
        <div class="person-card compact" onclick="openPersonDetail('${person.id}')">
          <div class="person-header">
            <div class="person-photo">
              ${photoElement}
            </div>
            <div class="person-basic-info">
              <h3>${person.name}</h3>
              <div class="role-badge ${roleClass}">${person.role}</div>
            </div>
          </div>
          <div class="person-compact-details">
            <div class="compact-info-row">
              <strong>Position:</strong>
              <span>${person.position}</span>
            </div>
            <div class="compact-info-row">
              <strong>Status:</strong>
              <span class="${statusClass}">${person.status}</span>
            </div>
            <div class="compact-info-row">
              <strong>Last Login:</strong>
              <span>${lastLogin}</span>
            </div>
          </div>
        </div>
      `;
    }

    // Setup event listeners
    function setupEventListeners() {
      // Search functionality
      const searchInput = document.getElementById('search-input');
      searchInput.addEventListener('input', handleSearch);

      // Filter buttons
      const filterButtons = document.querySelectorAll('.filter-btn');
      filterButtons.forEach(button => {
        button.addEventListener('click', handleFilter);
      });

      // Form submission
      const addPersonForm = document.getElementById('add-person-form');
      addPersonForm.addEventListener('submit', handleAddPerson);

      // Create user form submission
      const createUserForm = document.getElementById('create-user-form');
      createUserForm.addEventListener('submit', handleCreateUser);

      // Photo upload handling
      const photoUpload = document.getElementById('photo-upload');
      const photoUrl = document.getElementById('photo-url');

      if (photoUpload) {
        photoUpload.addEventListener('change', handlePhotoUpload);
      }

      if (photoUrl) {
        photoUrl.addEventListener('input', handlePhotoUrl);
      }
    }

    // Handle search functionality
    function handleSearch(event) {
      const searchTerm = event.target.value.toLowerCase();
      filteredStaffVolunteers = allStaffVolunteers.filter(person => {
        return person.name.toLowerCase().includes(searchTerm) ||
               person.position.toLowerCase().includes(searchTerm) ||
               person.email.toLowerCase().includes(searchTerm) ||
               person.role.toLowerCase().includes(searchTerm);
      });

      // Apply current filter as well
      applyCurrentFilter();
      renderStaffVolunteers();
    }

    // Handle filter functionality
    function handleFilter(event) {
      const filterButtons = document.querySelectorAll('.filter-btn');
      filterButtons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      currentFilter = event.target.dataset.filter;
      applyCurrentFilter();
      renderStaffVolunteers();
    }

    // Apply current filter to filtered results
    function applyCurrentFilter() {
      if (currentFilter === 'all') {
        return; // filteredStaffVolunteers already set by search
      }

      filteredStaffVolunteers = filteredStaffVolunteers.filter(person => {
        if (currentFilter === 'Active' || currentFilter === 'Inactive') {
          return person.status === currentFilter;
        } else {
          return person.role === currentFilter;
        }
      });
    }

    // Modal functions
    function openAddPersonModal() {
      // Add user attribution to the form
      window.authUtils.addUserAttributionToForm('add-person-form');

      document.getElementById('add-person-modal').style.display = 'flex';
    }

    function closeAddPersonModal() {
      document.getElementById('add-person-modal').style.display = 'none';
      document.getElementById('add-person-form').reset();
      document.getElementById('person-feedback').innerHTML = '';
      document.getElementById('photo-preview').style.display = 'none';
      switchPhotoMethod('upload');
    }

    // Create User Modal functions
    async function openCreateUserModal() {
      // First check if there are any staff/volunteers without user accounts
      try {
        const { data: availablePeople, error } = await supabase
          .from('staff_volunteers')
          .select('id, name, email, role')
          .is('user_id', null)
          .eq('status', 'Active');

        if (error) {
          console.error('Error fetching available people:', error);
          showToast('Error loading staff/volunteer data.', 'error');
          return;
        }

        if (!availablePeople || availablePeople.length === 0) {
          showToast('No staff or volunteer members available for user account creation. All active members already have user accounts.', 'info');
          return;
        }

        // Populate the dropdown
        const selectElement = document.getElementById('link-person');
        selectElement.innerHTML = '<option value="">Select a person to link...</option>';

        availablePeople.forEach(person => {
          const option = document.createElement('option');
          option.value = person.id;
          option.textContent = `${person.name} (${person.role})${person.email ? ' - ' + person.email : ''}`;
          option.dataset.email = person.email || '';
          option.dataset.role = person.role;
          selectElement.appendChild(option);
        });

        // Set up email auto-fill when person is selected
        selectElement.addEventListener('change', function() {
          const selectedOption = this.options[this.selectedIndex];
          if (selectedOption.dataset.email) {
            document.getElementById('user-email').value = selectedOption.dataset.email;
          }
          // Auto-suggest user role based on staff/volunteer role
          const personRole = selectedOption.dataset.role;
          const userRoleSelect = document.getElementById('user-role');
          if (personRole === 'Staff') {
            userRoleSelect.value = 'Staff';
          } else if (personRole === 'Volunteer') {
            userRoleSelect.value = 'Volunteer';
          }
        });

        // Add user attribution to the form
        window.authUtils.addUserAttributionToForm('create-user-form');

        document.getElementById('create-user-modal').style.display = 'flex';
      } catch (error) {
        console.error('Error opening create user modal:', error);
        showToast('Error loading available staff/volunteers.', 'error');
      }
    }

    function closeCreateUserModal() {
      document.getElementById('create-user-modal').style.display = 'none';
      document.getElementById('create-user-form').reset();
      document.getElementById('create-user-feedback').innerHTML = '';
    }

    function openPersonDetail(personId) {
      const person = allStaffVolunteers.find(p => p.id === personId);
      if (!person) return;

      const photoUrl = person.photo_url || '';
      const photoElement = photoUrl
        ? `<img src="${photoUrl}" alt="${person.name}" style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover; margin-bottom: 1rem;" />`
        : `<div style="width: 120px; height: 120px; border-radius: 50%; background: rgba(255,255,255,0.3); display: flex; align-items: center; justify-content: center; margin-bottom: 1rem;"><span class="material-icons" style="font-size: 3rem; color: #666;">person</span></div>`;

      const roleClass = person.role === 'Staff' ? 'staff-badge' : 'volunteer-badge';
      const statusClass = person.status === 'Active' ? 'status-active' : 'status-inactive';
      const lastLogin = person.last_login ? new Date(person.last_login).toLocaleDateString('en-GB') : 'Never';

      document.getElementById('person-detail-content').innerHTML = `
        <div style="text-align: center;">
          ${photoElement}
          <h2>${person.name}</h2>
          <div class="role-badge ${roleClass}" style="margin-bottom: 1.5rem;">${person.role}</div>
        </div>
        <div class="person-detail-info">
          <div class="info-row">
            <strong>Position:</strong>
            <span>${person.position}</span>
          </div>
          <div class="info-row">
            <strong>Email:</strong>
            <span><a href="mailto:${person.email}">${person.email}</a></span>
          </div>
          <div class="info-row">
            <strong>Phone:</strong>
            <span>${person.phone_number ? `<a href="tel:${person.phone_number}">${person.phone_number}</a>` : 'Not provided'}</span>
          </div>
          <div class="info-row">
            <strong>Status:</strong>
            <span class="${statusClass}">${person.status}</span>
          </div>
          <div class="info-row">
            <strong>Start Date:</strong>
            <span>${new Date(person.start_date).toLocaleDateString('en-GB')}</span>
          </div>
          <div class="info-row">
            <strong>Last Login:</strong>
            <span>${lastLogin}</span>
          </div>
          <div class="info-row">
            <strong>Added:</strong>
            <span>${new Date(person.created_at).toLocaleDateString('en-GB')}</span>
          </div>
        </div>
        <div class="form-actions" style="margin-top: 2rem;">
          <button type="button" class="btn-secondary" onclick="editPerson('${person.id}')">Edit Person</button>
          <button type="button" class="btn-primary" onclick="closePersonDetailModal()">Close</button>
        </div>
      `;

      document.getElementById('person-detail-modal').style.display = 'flex';
    }

    function closePersonDetailModal() {
      document.getElementById('person-detail-modal').style.display = 'none';
    }

    function editPerson(personId) {
      // For now, just show an alert - you could implement edit functionality
      alert('Edit functionality would be implemented here. Person ID: ' + personId);
    }

    // Photo handling functions
    function switchPhotoMethod(method) {
      const uploadTab = document.getElementById('upload-tab');
      const urlTab = document.getElementById('url-tab');
      const uploadMethod = document.getElementById('upload-method');
      const urlMethod = document.getElementById('url-method');

      if (method === 'upload') {
        uploadTab.classList.add('active-tab');
        urlTab.classList.remove('active-tab');
        uploadMethod.style.display = 'block';
        urlMethod.style.display = 'none';
        document.getElementById('photo-url').value = '';
      } else {
        urlTab.classList.add('active-tab');
        uploadTab.classList.remove('active-tab');
        urlMethod.style.display = 'block';
        uploadMethod.style.display = 'none';
        document.getElementById('photo-upload').value = '';
      }
      document.getElementById('photo-preview').style.display = 'none';
    }

    function handlePhotoUpload(event) {
      const file = event.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          document.getElementById('preview-image').src = e.target.result;
          document.getElementById('photo-preview').style.display = 'block';
        };
        reader.readAsDataURL(file);
      }
    }

    function handlePhotoUrl(event) {
      const url = event.target.value;
      if (url) {
        document.getElementById('preview-image').src = url;
        document.getElementById('photo-preview').style.display = 'block';
      } else {
        document.getElementById('photo-preview').style.display = 'none';
      }
    }

    function removePhoto() {
      document.getElementById('photo-upload').value = '';
      document.getElementById('photo-url').value = '';
      document.getElementById('photo-preview').style.display = 'none';
    }

    // Handle form submission
    async function handleAddPerson(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const personData = {
        name: formData.get('name'),
        role: formData.get('role'),
        position: formData.get('position'),
        email: formData.get('email'),
        phone_number: formData.get('phone_number') || null,
        start_date: formData.get('start_date'),
        status: formData.get('status') || 'Active',
        photo_url: formData.get('photo_url') || null
      };

      // Basic validation
      if (!personData.name || !personData.role || !personData.position || !personData.email || !personData.start_date) {
        showToast('Please fill in all required fields.', 'error');
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(personData.email)) {
        showToast('Please enter a valid email address.', 'error');
        return;
      }

      try {
        // Handle photo upload if file is selected
        const photoFile = formData.get('photo');
        if (photoFile && photoFile.size > 0) {
          // For now, we'll skip file upload to Supabase storage and just use URL
          // In a full implementation, you'd upload to Supabase storage here
          personData.photo_url = null; // Reset since we're not handling file upload yet
        }

        // Add user attribution
        const personDataWithAttribution = window.authUtils.addUserAttribution(personData, false);

        const { data, error } = await supabase
          .from('staff_volunteers')
          .insert([personDataWithAttribution]);

        if (error) {
          console.error('Error adding person:', error);
          if (error.message && error.message.includes('duplicate key')) {
            showToast('This email address is already in use.', 'error');
          } else {
            showToast('Error adding person. Please try again.', 'error');
          }
        } else {
          showToast('Person added successfully!', 'success');
          setTimeout(() => {
            closeAddPersonModal();
            loadStaffVolunteers(); // Reload the list
          }, 1500);
        }
      } catch (error) {
        console.error('Error adding person:', error);
        showToast('Network error. Please check your connection and try again.', 'error');
      }
    }

    // Handle create user form submission
    async function handleCreateUser(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const userData = {
        person_id: formData.get('person_id'),
        email: formData.get('email'),
        user_role: formData.get('user_role'),
        password: formData.get('password'),
        requireReset: formData.get('requireReset') === 'on'
      };

      // Validation
      if (!userData.person_id || !userData.email || !userData.user_role || !userData.password) {
        showToast('Please fill in all required fields.', 'error');
        return;
      }

      // Validate password requirements
      if (!validatePassword(userData.password)) {
        document.getElementById('create-user-feedback').innerHTML =
          '<div class="feedback error">Password does not meet requirements.</div>';
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        showToast('Please enter a valid email address.', 'error');
        return;
      }

      try {
        document.getElementById('create-user-feedback').innerHTML =
          '<div class="feedback info">Creating user account...</div>';

        // Get the selected person's details
        const { data: personData, error: personError } = await supabase
          .from('staff_volunteers')
          .select('*')
          .eq('id', userData.person_id)
          .single();

        if (personError || !personData) {
          throw new Error('Selected staff/volunteer member not found.');
        }

        // Check if person already has a user account
        if (personData.user_id) {
          throw new Error('This person already has a user account.');
        }

        // Create user in Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: userData.email,
          password: userData.password,
          email_confirm: true
        });

        if (authError) {
          throw authError;
        }

        // Update the staff_volunteers record with the new user_id and role
        const { error: updateError } = await supabase
          .from('staff_volunteers')
          .update({
            user_id: authData.user.id,
            user_role: userData.user_role,
            password_reset_required: userData.requireReset,
            updated_at: new Date().toISOString()
          })
          .eq('id', userData.person_id);

        if (updateError) {
          // If updating staff_volunteers fails, we should clean up the auth user
          console.error('Failed to link user to staff member:', updateError);
          throw new Error('Failed to link user account to staff member.');
        }

        document.getElementById('create-user-feedback').innerHTML =
          '<div class="feedback success">User account created successfully!</div>';

        showToast('User account created and linked successfully!', 'success');

        setTimeout(() => {
          closeCreateUserModal();
          loadStaffVolunteers(); // Reload the list to show updated data
        }, 2000);

      } catch (error) {
        console.error('User creation error:', error);
        document.getElementById('create-user-feedback').innerHTML =
          `<div class="feedback error">${error.message || 'Failed to create user account.'}</div>`;
      }
    }

    // Validate password requirements
    function validatePassword(password) {
      const minLength = password.length >= 12;
      const hasUpper = /[A-Z]/.test(password);
      const hasLower = /[a-z]/.test(password);
      const hasNumber = /\d/.test(password);
      const hasSpecial = /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password);

      return minLength && hasUpper && hasLower && hasNumber && hasSpecial;
    }

    // Toggle password visibility
    function togglePasswordVisibility(inputId) {
      const input = document.getElementById(inputId);
      const button = input.nextElementSibling;
      const icon = button.querySelector('.material-icons');

      if (input.type === 'password') {
        input.type = 'text';
        icon.textContent = 'visibility_off';
      } else {
        input.type = 'password';
        icon.textContent = 'visibility';
      }
    }

    // GDPR Modal functions
    function openGDPRModal() {
      document.getElementById('gdpr-modal').style.display = 'flex';
    }

    function closeGDPRModal() {
      document.getElementById('gdpr-modal').style.display = 'none';
    }

    // Utility functions
    function setGDPRDate() {
      const today = new Date().toLocaleDateString('en-GB');
      document.getElementById('gdpr-date').textContent = today;
    }

    function setDefaultStartDate() {
      const today = new Date().toISOString().split('T')[0];
      document.getElementById('person-start-date').value = today;
    }

    function showEmptyState() {
      document.getElementById('staff-volunteers-grid').innerHTML = '';
      document.getElementById('empty-state').style.display = 'block';
    }

    // Make functions globally available
    window.openAddPersonModal = openAddPersonModal;
    window.closeAddPersonModal = closeAddPersonModal;
    window.openCreateUserModal = openCreateUserModal;
    window.closeCreateUserModal = closeCreateUserModal;
    window.openPersonDetail = openPersonDetail;
    window.closePersonDetailModal = closePersonDetailModal;
    window.editPerson = editPerson;
    window.switchPhotoMethod = switchPhotoMethod;
    window.removePhoto = removePhoto;
    window.togglePasswordVisibility = togglePasswordVisibility;
    window.openGDPRModal = openGDPRModal;
    window.closeGDPRModal = closeGDPRModal;
    window.toggleMenu = toggleMenu;

    // Toast notification function
    function showToast(message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastMessage = document.getElementById('toast-message');

      toastMessage.textContent = message;
      toast.className = `toast ${type} show`;

      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }
  </script>

  <!-- Toast Notification -->
  <div id="toast" class="toast">
    <span id="toast-message"></span>
  </div>
</body>
</html>
